@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=E:\ProjectCode\GoCode\wails-kickscrew-print-label\frontend\node_modules\.pnpm\@babel+parser@7.26.2\node_modules\@babel\parser\bin\node_modules;E:\ProjectCode\GoCode\wails-kickscrew-print-label\frontend\node_modules\.pnpm\@babel+parser@7.26.2\node_modules\@babel\parser\node_modules;E:\ProjectCode\GoCode\wails-kickscrew-print-label\frontend\node_modules\.pnpm\@babel+parser@7.26.2\node_modules\@babel\node_modules;E:\ProjectCode\GoCode\wails-kickscrew-print-label\frontend\node_modules\.pnpm\@babel+parser@7.26.2\node_modules;E:\ProjectCode\GoCode\wails-kickscrew-print-label\frontend\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=E:\ProjectCode\GoCode\wails-kickscrew-print-label\frontend\node_modules\.pnpm\@babel+parser@7.26.2\node_modules\@babel\parser\bin\node_modules;E:\ProjectCode\GoCode\wails-kickscrew-print-label\frontend\node_modules\.pnpm\@babel+parser@7.26.2\node_modules\@babel\parser\node_modules;E:\ProjectCode\GoCode\wails-kickscrew-print-label\frontend\node_modules\.pnpm\@babel+parser@7.26.2\node_modules\@babel\node_modules;E:\ProjectCode\GoCode\wails-kickscrew-print-label\frontend\node_modules\.pnpm\@babel+parser@7.26.2\node_modules;E:\ProjectCode\GoCode\wails-kickscrew-print-label\frontend\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\..\bin\babel-parser.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\..\bin\babel-parser.js" %*
)
